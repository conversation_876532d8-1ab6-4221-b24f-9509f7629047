import makePostMediaFromFiles from 'shared/utils/toolkit/makePostMediaFromFiles';
import type { StageCardProps } from '@shared/components/atoms/containers/StageCard';
import type { CompareVariant } from '@shared/components/Organism/CompareModal';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type { BECandidateSearchResult } from '@shared/types/candidates';
import type { JobAPIProps } from '@shared/types/jobsProps';
import type { PageType } from '@shared/types/page';
import type { UserType } from '@shared/types/user';
import type { FormProps } from '@shared/uikit/Form';
import type { SharePanelDataAttachment } from 'shared/types/attachment';
import type { ShareEntityTab } from 'shared/types/share/entities';

type MediasType = Array<{ type: 'image' | 'video'; path: string; id: string }>;

export const initialState = {
  isOpenChatPanel: false,
  isOpenLeftPanel: false,
  isOpenRightPanel: false,
  isOpenProfilePanel: false,
  isOpenNotificationModal: false,
  activeRoom: undefined,
  selectedHashtag: undefined,
  isCreateEntityPanelOpen: false,
  isDrawerOpen: false,
  visibleLeavePageConfirm: true,
  isSettingModalOpen: false,
  isLinkedJobModal: false,
  isFeedbackModalOpen: false,
  isTakeABreakModalOpen: false,
  defaultSettingsExpanded: 0,
  isRecommendationOpenedFromMessage: false,
  parentPathname: '',
  uploadingPostFiles: false,
  createPostProgress: [] as number[],
  duplicationVendorsModal: {
    isOpen: false,
    id: '',
    vendorSubmitted: {} as StageCardProps.VendorSubmitted,
  },
  createPostModal: {
    isOpenModal: false,
    currentTab: 'main',
    files: null,
    attachment: null,
    defaultActiveH: undefined,
    form: undefined as FormProps | undefined,
    isPrevStepMainTab: undefined,
    chosenHighlight: null as any,
    selectedMedia: [] as File[] | File | undefined,
  },
  createdPostData: {
    data: {},
    sender: '',
  },
  objectNetworkModal: {
    isOpen: false,
    currentTab: null,
    objectId: null,
    objectTitle: null,
    network: null,
    isPage: null,
  },
  reportModal: {
    isOpen: false,
    data: undefined,
  },
  confirmBlockModal: {
    isOpen: false,
    data: undefined,
  },
  viewMedia: {
    isOpen: false,
    medias: [],
    selectedItem: 0,
  },
  mutualModal: {
    isOpen: undefined,
    data: undefined,
  },
  shareEntityTabbedModal: {
    isOpen: false,
    entityData: {
      attachment: {} as SharePanelDataAttachment | undefined,
      showCopyId: false,
    },
    tabs: [] as ShareEntityTab[],
  },
  isOpenCreateJobModalInUserProject: false,
  profileHistoryCount: 0,
  scheduleHistoryCount: 0,
  scheduleEventHistoryCount: 0,
  searchHistoryCount: 0,
  isScheduleSubPage: false,
  isSearchSubPage: false,
  isSearchStartedFromMain: false,
  profilePanelState: {},
  invitationFailuresModal: {
    isOpen: false,
    failureId: '',
  },
  invitationsListModal: {
    isOpen: false,
  },
  isOpenManageCookieModal: false,
  candidateManager: {
    isOpen: false,
    enablePagination: true,
    showScoreSection: false,
  } as {
    isOpen: boolean;
    enablePagination: boolean;
    tab?: CandidateManagerTabkeys | undefined;
    entity?: string;
    totalElements?: number;
    currentIndex?: number;
    apiFunc?: (params: any) => Promise<any>;
    entityId?: string;
    showScoreSection?: boolean;
  },
  mediaLightBoxFeedId: false,
  commentModalData: undefined,
  reactionsModalData: undefined,
  editAssigneeModalData: {
    open: false,
  } as {
    open: boolean;
    data?: any;
    type?: 'job' | 'project';
  },
  compareModal: {
    open: false,
    variant: 'editable',
    selectedUsers: [],
    onBack: undefined,
  } as {
    open: boolean;
    data?: JobAPIProps;
    variant?: 'editable' | 'readonly';
    onBack?: () => void;

    selectedUsers?: BECandidateSearchResult[];
  },

  autoMessageModal: {
    isOpen: false,
    data: undefined,
  },
  searchPortalName: undefined,
};
export type ReportEntityType =
  | 'user'
  | 'page'
  | 'post'
  | 'comment'
  | 'meeting'
  | 'job'
  | 'message';

export type ActionType =
  | { type: 'OPEN_MESSAGE_PANEL' }
  | { type: 'CLOSE_MESSAGE_PANEL' }
  | { type: 'OPEN_LEFT_PANEL' }
  | { type: 'CLOSE_LEFT_PANEL' }
  | {
      type: 'OPEN_DUPLICATION_VENDORS_PANEL';
      payload: { id: string; vendorSubmitted?: StageCardProps.VendorSubmitted };
    }
  | {
      type: 'CLOSE_DUPLICATION_VENDORS_PANEL';
      payload?: { id: string; vendorSubmitted: undefined };
    }
  | { type: 'SET_DEFAULT_ACTIVE_ROOM'; payload: { room: any } }
  | { type: 'OPEN_OBJECT_NETWORK_MODAL'; payload: any }
  | { type: 'PARENT_PATHNAME'; payload: string }
  | { type: 'CLOSE_OBJECT_NETWORK_MODAL' }
  | {
      type: 'TOGGLE_REPORT_MODAL';
      payload: {
        isOpen: boolean;
        data?: {
          entityType: ReportEntityType;
          entityId: string;
        };
      };
    }
  | {
      type: 'TOGGLE_CONFIRM_BLOCK_MODAL';
      payload: {
        isOpen: boolean;
        data?: {
          objectDetail: Partial<UserType | PageType>;
          onSuccess: (arg: any) => void;
        };
      };
    }
  | {
      type: 'TOGGLE_IS_OPENED_RECOMMENDATION_FROM_MESSAGE';
      payload: {
        isOpened: boolean;
      };
    }
  | { type: 'TOGGLE_CREATE_ENTITY_PANEL' }
  | { type: 'TOGGLE_MANAGE_COOKIE_MODAL' }
  | { type: 'CLOSE_CREATE_ENTITY_PANEL' }
  | { type: 'TOGGLE_SETTINGS_MODAL' }
  | { type: 'TOGGLE_LINKED_JOB_MODAL' }
  | { type: 'TOGGLE_FEEDBACK_MODAL' }
  | { type: 'SHOW_LEAVE_CREATE_PAGE_CONFIRM' }
  | { type: 'HIDE_LEAVE_CREATE_PAGE_CONFIRM' }
  | { type: 'CLOSE_VIEW_MEDIA' }
  | {
      type: 'TOGGLE_CANDIDATE_MANAGER';
      payload?: {
        isOpen: boolean;
        tab?: CandidateManagerTabkeys;
        totalElements?: number;
        currentIndex?: number;
        entity?: string;
        apiFunc?: (params: any) => Promise<any>;
        entityId?: string;
        showScoreSection?: boolean;
        enablePagination?: boolean;
      };
    }
  | {
      type: 'OPEN_VIEW_MEDIA';
      payload: { medias: MediasType; selectedItem: number };
    }
  | {
      type: 'SET_SETTINGS_DEFAULT_EXPANDED';
      payload: { defaultSettingsExpanded: number };
    }
  | {
      type: 'SET_SHOW_RIGHT_PANEL';
      payload: boolean;
    }
  | {
      type: 'SET_IS_OPEN_NOTIFICATION_PANEL';
      payload: boolean;
    }
  | {
      type: 'SET_SHOW_PROFILE_PANEL';
      payload: boolean | ProfilePanelStateType;
    }
  | { type: 'TOGGLE_MUTUAL_MODAL'; payload: any }
  | { type: 'SET_UPLOADING_POST_FILES'; payload: boolean }
  | {
      type: 'SET_CREATE_POST_PROGRESS';
      payload: { index: number; value: number };
    }
  | { type: 'RESET_CREATE_POST_PROGRESS' }
  | { type: 'SET_CREATE_POST_MODAL'; payload: any }
  | { type: 'SET_CREATED_POST_DATA'; payload: { sender: string; data: any } }
  | { type: 'RESET_CREATED_POST_DATA' }
  | {
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA';
      payload: Partial<StateType['shareEntityTabbedModal']>;
    }
  | {
      type: 'TOGGLE_CREATE_JOB_MODAL_IN_USER_PROJECT';
      payload: boolean;
    }
  | {
      type: 'ADD_PROFILE_HISTORY_COUNT';
    }
  | {
      type: 'RESET_PROFILE_HISTORY_COUNT';
    }
  | {
      type: 'RESET_CREATE_POST_MODAL';
    }
  | {
      type: 'ADD_SCHEDULE_HISTORY_COUNT';
    }
  | {
      type: 'SET_SCHEDULE_HISTORY_COUNT';
      payload: number;
    }
  | {
      type: 'RESET_SCHEDULE_HISTORY_COUNT';
    }
  | {
      type: 'ADD_SCHEDULE_EVENT_HISTORY_COUNT';
    }
  | {
      type: 'RESET_SCHEDULE_EVENT_HISTORY_COUNT';
    }
  | {
      type: 'SET_SCHEDULE_SUBPAGE';
    }
  | {
      type: 'RESET_SCHEDULE_SUBPAGE';
    }
  | {
      type: 'ADD_SEARCH_HISTORY_COUNT';
    }
  | {
      type: 'SET_SEARCH_HISTORY_COUNT';
      payload: number;
    }
  | {
      type: 'RESET_SEARCH_HISTORY_COUNT';
    }
  | {
      type: 'SET_SEARCH_SUBPAGE';
    }
  | {
      type: 'RESET_SEARCH_SUBPAGE';
    }
  | {
      type: 'SET_SEARCH_STARTED_FROM_MAIN';
    }
  | {
      type: 'RESET_SEARCH_STARTED_FROM_MAIN';
    }
  | {
      type: 'OPEN_INVITATION_FAILURE_MODAL';
      payload: {
        failureId: string;
      };
    }
  | { type: 'CLOSE_INVITATION_FAILURE_MODAL' }
  | { type: 'OPEN_INVITATION_LIST_MODAL' }
  | { type: 'CLOSE_INVITATION_LIST_MODAL' }
  | {
      type: 'RESET_ON_NAVIGATION';
    }
  | {
      type: 'OPEN_EDIT_ASSIGNEE_MODAL';
      payload: { entity: any; type: 'project' | 'job' };
    }
  | {
      type: 'TOGGLE_MEDIA_LIGHT_BOX';
      payload: boolean;
    }
  | {
      type: 'TOGGLE_COMMENTS_MODAL';
      payload: any;
    }
  | {
      type: 'TOGGLE_FEED_REACTIONS_MODAL';
      payload: any;
    }
  | {
      type: 'CLOSE_EDIT_ASSIGNEE_MODAL';
    }
  | {
      type: 'TOGGLE_COMPARE_MODAL';
      payload: {
        open: boolean;
        data?: JobAPIProps;
        selectedUsers?: BECandidateSearchResult[];
        variant?: CompareVariant;
      };
    }
  | {
      type: 'TOGGLE_AUTO_MESSAGE_MODAL';
      payload: { open: boolean; data?: any };
    };

export interface ProfilePanelStateType {
  settings?: {
    preferences?: {
      schedule?: {
        availability?: boolean;
        calendar?: boolean;
        meeting?: boolean;
        icloudCalendarIntegration?: boolean;
      };
    };
  };
  previous?: ProfilePanelStateType | false;
}

export type StateType = typeof initialState & {
  profilePanelState: ProfilePanelStateType;
};

export function globalReducer(state: StateType, action: ActionType): StateType {
  switch (action.type) {
    case 'OPEN_MESSAGE_PANEL':
      return {
        ...state,
        isOpenChatPanel: true,
      };
    case 'CLOSE_MESSAGE_PANEL':
      return {
        ...state,
        isOpenChatPanel: false,
      };
    case 'OPEN_LEFT_PANEL':
      return {
        ...state,
        isOpenLeftPanel: true,
      };
    case 'CLOSE_LEFT_PANEL':
      return {
        ...state,
        isOpenLeftPanel: false,
      };
    case 'OPEN_DUPLICATION_VENDORS_PANEL':
      return {
        ...state,
        duplicationVendorsModal: {
          isOpen: true,
          id: action.payload.id,
          vendorSubmitted: action.payload.vendorSubmitted,
        },
      };
    case 'CLOSE_DUPLICATION_VENDORS_PANEL':
      return {
        ...state,
        duplicationVendorsModal: {
          isOpen: false,
          id: '',
          vendorSubmitted: undefined,
        },
      };

    case 'TOGGLE_CREATE_ENTITY_PANEL':
      return {
        ...state,
        isCreateEntityPanelOpen: !state.isCreateEntityPanelOpen,
      };
    case 'TOGGLE_MANAGE_COOKIE_MODAL':
      return {
        ...state,
        isOpenManageCookieModal: !state.isOpenManageCookieModal,
      };
    case 'CLOSE_CREATE_ENTITY_PANEL':
      return {
        ...state,
        isCreateEntityPanelOpen: false,
      };
    case 'TOGGLE_SETTINGS_MODAL':
      return {
        ...state,
        isSettingModalOpen: !state.isSettingModalOpen,
      };
    case 'TOGGLE_LINKED_JOB_MODAL':
      return {
        ...state,
        isLinkedJobModal: !state.isLinkedJobModal,
      };
    case 'TOGGLE_FEEDBACK_MODAL':
      return {
        ...state,
        isFeedbackModalOpen: !state.isFeedbackModalOpen,
      };
    case 'OPEN_OBJECT_NETWORK_MODAL':
      return {
        ...state,
        objectNetworkModal: {
          isOpen: true,
          ...(action.payload || {}),
        },
      };
    case 'OPEN_INVITATION_FAILURE_MODAL':
      return {
        ...state,
        invitationFailuresModal: {
          isOpen: true,
          failureId: action?.payload?.failureId,
        },
      };
    case 'CLOSE_INVITATION_FAILURE_MODAL':
      return {
        ...state,
        invitationFailuresModal: {
          isOpen: false,
          failureId: '',
        },
      };
    case 'OPEN_INVITATION_LIST_MODAL':
      return {
        ...state,
        invitationsListModal: {
          isOpen: true,
        },
      };
    case 'CLOSE_INVITATION_LIST_MODAL':
      return {
        ...state,
        invitationsListModal: {
          isOpen: false,
        },
      };
    case 'PARENT_PATHNAME':
      return {
        ...state,
        parentPathname: action.payload,
      };
    case 'CLOSE_OBJECT_NETWORK_MODAL':
      return {
        ...state,
        objectNetworkModal: {
          isOpen: false,
          currentTab: null,
          objectId: null,
          objectTitle: null,
          network: null,
          isPage: null,
        },
      };
    case 'SHOW_LEAVE_CREATE_PAGE_CONFIRM':
      return {
        ...state,
        visibleLeavePageConfirm: true,
      };
    case 'SET_DEFAULT_ACTIVE_ROOM':
      return {
        ...state,
        activeRoom: action.payload.room,
      };
    case 'SET_SHOW_RIGHT_PANEL':
      return {
        ...state,
        isOpenRightPanel: action.payload,
      };
    case 'SET_IS_OPEN_NOTIFICATION_PANEL':
      return {
        ...state,
        isOpenNotificationModal: action.payload,
      };
    case 'SET_SHOW_PROFILE_PANEL':
      return {
        ...state,
        ...(typeof action.payload === 'boolean'
          ? { isOpenProfilePanel: action.payload, profilePanelState: {} }
          : { isOpenProfilePanel: true, profilePanelState: action.payload }),
      };
    case 'HIDE_LEAVE_CREATE_PAGE_CONFIRM':
      return {
        ...state,
        visibleLeavePageConfirm: false,
      };
    case 'SET_SETTINGS_DEFAULT_EXPANDED':
      return {
        ...state,
        defaultSettingsExpanded: action.payload.defaultSettingsExpanded,
      };
    case 'TOGGLE_REPORT_MODAL':
      return {
        ...state,
        reportModal: {
          isOpen: action.payload.isOpen,
          data: action.payload.data as any,
        },
      };
    case 'TOGGLE_CONFIRM_BLOCK_MODAL':
      return {
        ...state,
        confirmBlockModal: {
          isOpen: action.payload?.isOpen,
          data: action.payload?.data as any,
        },
      };
    case 'TOGGLE_MUTUAL_MODAL':
      return {
        ...state,
        mutualModal: {
          isOpen: action.payload.isOpen,
          data: action.payload.data as any,
        },
      };
    case 'SET_UPLOADING_POST_FILES': {
      return {
        ...state,
        uploadingPostFiles: action.payload,
      };
    }
    case 'RESET_CREATE_POST_PROGRESS': {
      return {
        ...state,
        createPostProgress: [],
      };
    }
    case 'SET_CREATE_POST_PROGRESS': {
      const createPostProgress = [...state.createPostProgress];
      createPostProgress[action.payload.index] = action.payload.value;

      return {
        ...state,
        createPostProgress,
      };
    }
    case 'RESET_CREATE_POST_MODAL': {
      return {
        ...state,
        createPostModal: initialState.createPostModal,
      };
    }
    case 'SET_CREATE_POST_MODAL': {
      const selectedMedia = action.payload.selectedMedia
        ? makePostMediaFromFiles(action.payload.selectedMedia)
        : undefined;

      return {
        ...state,
        createPostModal: {
          ...state.createPostModal,
          ...action.payload,
          selectedMedia,
        },
      };
    }
    case 'SET_CREATED_POST_DATA': {
      return {
        ...state,
        createdPostData: {
          ...action.payload,
        },
      };
    }
    case 'RESET_CREATED_POST_DATA': {
      return {
        ...state,
        createdPostData: {
          sender: '',
          data: {},
        },
      };
    }
    case 'OPEN_VIEW_MEDIA':
      return {
        ...state,
        viewMedia: {
          selectedItem: action.payload.selectedItem,
          isOpen: true,
          // @ts-ignore
          medias: action.payload.medias,
        },
      };
    case 'CLOSE_VIEW_MEDIA':
      return {
        ...state,
        viewMedia: {
          isOpen: false,
          medias: [],
          selectedItem: 0,
        },
      };
    case 'TOGGLE_IS_OPENED_RECOMMENDATION_FROM_MESSAGE':
      return {
        ...state,
        isRecommendationOpenedFromMessage: action.payload.isOpened,
      };
    case 'TOGGLE_CANDIDATE_MANAGER':
      return {
        ...state,
        candidateManager: {
          ...state.candidateManager,
          ...(action.payload ?? { isOpen: false }),
        },
      };
    case 'SET_SHARE_ENTITY_TABBED_MODAL_DATA':
      return {
        ...state,
        shareEntityTabbedModal: {
          ...state?.shareEntityTabbedModal,
          ...action?.payload,
        },
      };
    case 'TOGGLE_CREATE_JOB_MODAL_IN_USER_PROJECT':
      return {
        ...state,
        isOpenCreateJobModalInUserProject: action.payload,
      };
    case 'ADD_PROFILE_HISTORY_COUNT':
      return {
        ...state,
        profileHistoryCount: state.profileHistoryCount + 1,
      };
    case 'RESET_PROFILE_HISTORY_COUNT':
      return {
        ...state,
        profileHistoryCount: 0,
      };
    case 'ADD_SCHEDULE_HISTORY_COUNT':
      return {
        ...state,
        scheduleHistoryCount: state.scheduleHistoryCount + 1,
      };
    case 'SET_SCHEDULE_HISTORY_COUNT':
      return {
        ...state,
        scheduleHistoryCount: action.payload,
      };
    case 'RESET_SCHEDULE_HISTORY_COUNT':
      return {
        ...state,
        scheduleHistoryCount: 0,
      };
    case 'SET_SCHEDULE_SUBPAGE':
      return {
        ...state,
        isScheduleSubPage: true,
      };
    case 'RESET_SCHEDULE_SUBPAGE':
      return {
        ...state,
        isScheduleSubPage: false,
      };
    case 'ADD_SEARCH_HISTORY_COUNT':
      return {
        ...state,
        searchHistoryCount: state.searchHistoryCount + 1,
      };
    case 'SET_SEARCH_HISTORY_COUNT':
      return {
        ...state,
        searchHistoryCount: action.payload,
      };
    case 'RESET_SEARCH_HISTORY_COUNT':
      return {
        ...state,
        searchHistoryCount: 0,
      };
    case 'SET_SEARCH_SUBPAGE':
      return {
        ...state,
        isSearchSubPage: true,
      };
    case 'RESET_SEARCH_SUBPAGE':
      return {
        ...state,
        isSearchSubPage: false,
      };
    case 'SET_SEARCH_STARTED_FROM_MAIN':
      return {
        ...state,
        isSearchStartedFromMain: true,
      };
    case 'RESET_SEARCH_STARTED_FROM_MAIN':
      return {
        ...state,
        isSearchStartedFromMain: false,
      };

    case 'RESET_ON_NAVIGATION':
      return {
        ...state,
        invitationFailuresModal: initialState?.invitationFailuresModal,
        invitationsListModal: initialState?.invitationsListModal,
        isOpenRightPanel: initialState?.isOpenRightPanel,
        profilePanelState: initialState?.profilePanelState,
      };

    case 'OPEN_EDIT_ASSIGNEE_MODAL':
      return {
        ...state,
        editAssigneeModalData: {
          open: true,
          data: action.payload.entity,
          type: action.payload.type,
        },
      };
    case 'CLOSE_EDIT_ASSIGNEE_MODAL':
      return {
        ...state,
        editAssigneeModalData: {
          open: false,
        },
      };
    case 'TOGGLE_MEDIA_LIGHT_BOX':
      return {
        ...state,
        mediaLightBoxFeedId: action.payload,
        commentModalData: undefined,
      };
    case 'TOGGLE_FEED_REACTIONS_MODAL':
      return {
        ...state,
        reactionsModalData: action.payload,
        mediaLightBoxFeedId: false,
      };
    case 'TOGGLE_COMMENTS_MODAL':
      return {
        ...state,
        commentModalData: action.payload,
      };
    case 'TOGGLE_COMPARE_MODAL':
      return {
        ...state,
        compareModal: {
          ...action.payload,
          selectedUsers: action.payload?.selectedUsers || [],
        },
      };

    case 'TOGGLE_AUTO_MESSAGE_MODAL':
      return {
        ...state,
        autoMessageModal: {
          isOpen: action.payload.open,
          data: action.payload.data,
        },
      };

    default: {
      throw new Error(
        `${action.type} action type is not supported at APP Reducer`
      );
    }
  }
}
