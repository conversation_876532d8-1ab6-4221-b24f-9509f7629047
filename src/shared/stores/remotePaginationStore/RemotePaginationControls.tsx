import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { useManagerContext } from '@shared/components/Organism/CandidateManager/CandidateManager.context';
import { useEffect, useRef } from 'react';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';

export default function RemotePaginationControls() {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { handleChangeParams } = useCustomParams();
  const candidateManagerState = useGlobalState('candidateManager');
  const entity = candidateManagerState?.entity;
  const currentIndex = candidateManagerState?.currentIndex || 0;
  const apiFunc = candidateManagerState?.apiFunc;
  const entityId = candidateManagerState?.entityId;

  const totalElements = candidateManagerState?.totalElements || 0;

  const { setCandidate, candidate, setIsLoading } = useManagerContext();
  const firstRender = useRef(true);
  const { mutate: getNextCandidate, isPending } = useReactMutation({
    apiFunc: async (params: any) => {
      const result = await apiFunc?.({ ...params, id: entityId });
      setCandidate(result);
      return result;
    },
  });

  const handleGetNextCandidate = () => {
    getNextCandidate(
      { number: currentIndex + 1 },
      {
        onSuccess: (res) => {
          appDispatch({
            type: 'TOGGLE_CANDIDATE_MANAGER',
            payload: {
              ...candidateManagerState,
              currentIndex: currentIndex + 1,
            },
          });
          if (res && entity) {
            handleChangeParams({
              replace: {
                [entity]: res.id,
              },
            });
          }
        },
      }
    );
  };

  const handleGetPreviousCandidate = () => {
    if (currentIndex > 0) {
      getNextCandidate(
        { number: currentIndex - 1 },
        {
          onSuccess: (res) => {
            appDispatch({
              type: 'TOGGLE_CANDIDATE_MANAGER',
              payload: {
                ...candidateManagerState,
                currentIndex: currentIndex - 1,
              },
            });
            if (res && entity) {
              handleChangeParams({
                replace: {
                  [entity]: res.id,
                },
              });
            }
          },
        }
      );
    }
  };

  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      getNextCandidate({ number: currentIndex });
    }
  }, [currentIndex]);

  useEffect(() => {
    setIsLoading(isPending);
  }, [isPending]);

  if (!candidateManagerState?.enablePagination) {
    return <Flex />;
  }

  return (
    <Flex flexDir="row" className="items-center gap-3">
      <IconButton
        name="chevron-left"
        size="md15"
        disabled={currentIndex <= 0 || isPending || totalElements === 0}
        aria-label="Previous page"
        onClick={handleGetPreviousCandidate}
      />

      <ParseTextStringCP
        textProps={{
          size: 20,
          font: '500',
          height: 23,
          color: 'secondaryDisabledText',
        }}
        textString={translateReplacer(t('x_of_y'), [
          String(currentIndex + 1),
          String(totalElements),
        ])}
        tagComponentMap={{
          0: (text) => (
            <Typography size={20} font="500" height={23}>
              {text}
            </Typography>
          ),
          1: (text) => (
            <Typography size={20} font="500" height={23}>
              {text}
            </Typography>
          ),
        }}
      />

      <IconButton
        disabled={
          !!(isPending || (totalElements && currentIndex >= totalElements - 1))
        }
        name="chevron-right"
        size="md15"
        aria-label="Next page"
        onClick={handleGetNextCandidate}
      />
    </Flex>
  );
}
