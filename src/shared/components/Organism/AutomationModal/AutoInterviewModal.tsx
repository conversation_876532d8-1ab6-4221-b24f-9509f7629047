import dayjs from 'dayjs';
import React, { useCallback } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { MeetingDatetimeType } from '@shared/types/schedules/schedules';
import Button from '@shared/uikit/Button';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import {
  deletePipelineAutoInterview,
  getPipelineAutoInterview,
  putPipelineAutoInterview,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { TemplateList } from './components';
import {
  useTemplateList,
  useTemplateActions,
  type TemplateAction,
  type NormalizedTemplate,
} from './hooks';

const AutoInterviewModal: React.FC = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const { openCreateEventWithDate } = useSchedulesCalendar();

  const {
    data: interview,
    isLoading: isLoadingInterview,
    refetch: refetchAutoInterview,
  } = useReactQuery({
    action: {
      apiFunc: () => getPipelineAutoInterview(pipelineId),
      key: [QueryKeys.getPipelineAutoInterview, pipelineId],
    },
  });

  const {
    mutate: putPipelineAutoInterviewMutation,
    isPending: isUpdatingPending,
  } = useReactMutation({
    apiFunc: putPipelineAutoInterview,
    onSuccess: () => {
      refetchAutoInterview();
    },
  });

  const {
    mutate: deletePipelineAutoInterviewMutation,
    isPending: isDeletePending,
  } = useReactMutation({
    apiFunc: deletePipelineAutoInterview,
    onSuccess: () => {
      refetchAutoInterview();
    },
  });

  const templateList = useTemplateList({
    searchEnabled: true,
  });

  const isDefaultTemplateLoading =
    isDeletePending || isUpdatingPending || isLoadingInterview;

  const templateActions = useTemplateActions(
    {
      onTemplateCreated: () => {
        templateList.refetch();
      },
      onTemplateUpdated: () => {
        templateList.refetch();
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: (templateId, isCurrentlyDefault) => {
        templateList.refetch();

        if (isCurrentlyDefault) {
          deletePipelineAutoInterviewMutation(pipelineId);
        } else {
          putPipelineAutoInterviewMutation({
            pipelineId,
            body: {
              templateId: Number(templateId),
            },
          });
        }
      },
    },
    {
      message: t('auto_reply_duplicated_message'),
      title: t('auto_reply_duplicated_title'),
    }
  );

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        handleEditTemplate();
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const handleCreate = () => {
    handleEditTemplate();
  };

  const handleTemplateClick = (templateId: string) => {
    const template = templateList.getTemplateById(templateId);
    if (template) {
      handleEditTemplate();
    }
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  // const isDefault = useMemo(
  //   () =>
  //     !!templateForm?.editingTemplate?.default &&
  //     !!interview?.templateId &&
  //     !!templateForm?.editingTemplate?.id &&
  //     templateForm?.editingTemplate?.id === interview?.templateId,
  //   [
  //     templateForm?.editingTemplate?.default,
  //     templateForm?.editingTemplate?.id,
  //     interview?.templateId,
  //   ]
  // );

  const handleEditTemplate = useCallback(() => {
    openCreateEventWithDate(
      dayjs().add(1, 'day'),
      {
        schedulesEventType: ScheduleEventTypes.MEETING,
        targetAttendee: {},
        datetimeType: MeetingDatetimeType.PROVIDE_AVAILABILITY,
        pipelineId,
        hasSetDefault: true,
        headerTitle: t('meeting_template'),
      },
      true
    );
  }, []);

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={t('auto_interview')}
        backButtonProps={{
          onClick: handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody className="p-6 h-full overflow-auto mb-8">
        <TemplateList
          templates={templateList.templates}
          isLoading={templateList.isLoading}
          searchQuery={templateList.searchQuery}
          defaultTemplateId={interview?.templateId}
          onSearchChange={templateList.handleSearchChange}
          onTemplateClick={handleTemplateClick}
          onSetDefault={handleSetDefault}
          actions={templateActionsList}
          isUpdatingDefault={templateActions.isUpdatingDefault}
          isDefaultTemplateLoading={isDefaultTemplateLoading}
          config={{
            showSearch: true,
            showActions: true,
            showDefaultToggle: true,
          }}
        />
      </ModalBody>

      <ModalFooter>
        <Button
          fullWidth
          label={t('create_template')}
          leftIcon="plus"
          leftType="fas"
          schema="semi-transparent"
          variant="default"
          onClick={handleCreate}
        />
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default AutoInterviewModal;
