import React, { useState } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import AutoComplete from '@shared/uikit/AutoComplete';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import MenuItem from '@shared/uikit/MenuItem';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useStageOptions } from './components';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import type { IconName } from '@shared/uikit/Icon/types';

interface StageOption {
  value: string;
  label: string;
  icon: ReactNode;
}

const AutomationDrawer: React.FC = () => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { data } = useMultiStepFormState('automation') as {
    data: PipelineInfo | undefined;
  };

  const stageOptions = useStageOptions();

  const [selectedStage, setSelectedStage] = useState<StageOption>(
    stageOptions.find(
      (opt) => opt.value.toLocaleLowerCase() === data?.type.toLocaleLowerCase()
    ) || stageOptions[0]
  );

  const automationFlags = {
    autoMovementEnabled: data?.autoMovementEnabled ?? false,
    autoRejectionEnabled: data?.autoRejectionEnabled ?? false,
    autoNoteEnabled: data?.autoNoteEnabled ?? false,
    autoTodoEnabled: data?.autoTodoEnabled ?? false,
    autoReplyEnabled: data?.autoReplyEnabled ?? false,
    autoMessageEnabled: data?.autoMessageEnabled ?? false,
    autoInterviewEnabled: true,
  };

  const handleClose = () => {
    closeMultiStepForm('automation');
  };

  const handleAutoMoveClick = () => {
    if (!automationFlags.autoMovementEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoMove',
    });
  };

  const handleAutoReplyClick = () => {
    if (!automationFlags.autoReplyEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoReply',
    });
  };

  const handleAutoInterviewClick = () => {
    if (!automationFlags.autoInterviewEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoInterview',
    });
  };

  const handleAutoMessageClick = () => {
    if (!automationFlags.autoMessageEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoMessage',
    });
  };

  const handleAutoNoteClick = () => {
    if (!automationFlags.autoNoteEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoNotes',
    });
  };

  const handleAutoTodoClick = () => {
    if (!automationFlags.autoTodoEnabled) return;
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data,
      type: 'autoTodo',
    });
  };

  const automationItems = [
    {
      icon: 'envelope' as IconName,
      title: t('auto_reply'),
      subtitle: t('c_t_t_r_t_y_c_f'),
      onClick: handleAutoReplyClick,
      enabled: automationFlags.autoReplyEnabled,
    },
    {
      icon: 'meeting' as IconName,
      title: t('auto_interview'),
      subtitle: t('s_i_a_b_o_t_s_w_c'),
      enabled: automationFlags.autoInterviewEnabled,
      onClick: handleAutoInterviewClick,
    },
    {
      icon: 'assessment' as IconName,
      title: t('auto_assessment'),
      subtitle: t('s_n_t_a_t_q_o_t_a'),
    },
    {
      icon: 'user-plus-s' as IconName,
      title: t('auto_move'),
      subtitle: t('m_c_t_o_o_b_s_q_s_s_a'),
      onClick: handleAutoMoveClick,
      enabled: automationFlags.autoMovementEnabled,
    },
    {
      icon: 'user-times-s' as IconName,
      title: t('auto_reject'),
      subtitle: t('r_c_b_o_f_q_s_s_a'),
      enabled: automationFlags.autoRejectionEnabled,
    },
    {
      icon: 'comment-alt-lines' as IconName,
      title: t('auto_message'),
      subtitle: t('c_t_t_r_t_y_c_f'),
      onClick: handleAutoMessageClick,
      enabled: automationFlags.autoMessageEnabled,
    },
    {
      icon: 'note' as IconName,
      title: t('auto_note'),
      subtitle: t('w_n_o_c_a'),
      onClick: handleAutoNoteClick,
      enabled: automationFlags.autoNoteEnabled,
    },
    {
      icon: 'checklist' as IconName,
      title: t('auto_todo'),
      subtitle: t('w_t_o_c_a_f_t_y'),
      onClick: handleAutoTodoClick,
      enabled: automationFlags.autoTodoEnabled,
    },
  ];

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple
        title={t('automation')}
        helper={t('s_c_b_o_t_c_n_a')}
      />
      <ModalBody className="!p-0">
        <Flex>
          <Flex className="flex-col bg-gray_5 px-20 py-6 w-full">
            <AutoComplete
              editable={false}
              visibleRightIcon
              variant="simple"
              value={selectedStage}
              onChangeInput={(value: any) => {
                const option = stageOptions.find(
                  (opt) => opt.value === value.value
                );
                if (option) {
                  setSelectedStage(option);
                }
              }}
              leftIcon={
                <Flex flexDir="row" alignItems="center" className="p-4">
                  {selectedStage.icon}
                </Flex>
              }
              inputWrapClassName="w-full"
              options={stageOptions}
              renderItem={({ item }) => (
                <Flex
                  flexDir="row"
                  alignItems="center"
                  className="w-full h-[40px] gap-10"
                >
                  {item.icon}
                  <Typography size={16}>{item.label}</Typography>
                </Flex>
              )}
              className="w-full"
              optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedStage.label}
              onSelect={(item: any) => {
                setSelectedStage(item);
              }}
            />
          </Flex>
          <Flex className="flex-col gap-4 py-20 px-12">
            {automationItems.map((item, index) => (
              <MenuItem
                key={index}
                onClick={item.enabled === false ? undefined : item.onClick}
                iconName={item.icon}
                iconType="far"
                iconSize={25}
                iconBoxSize={40}
                title={item.title}
                subTitle={item.subtitle}
                titleVariant="lg"
                withHover={item.enabled !== false}
                rightElement={
                  <Icon
                    color={item.enabled === false ? 'gray_30' : 'white'}
                    name="chevron-right"
                    type="fas"
                    size={20}
                  />
                }
                disabled={item.enabled === false}
                statusLabel={item.enabled ? t('activated') : undefined}
                statusLabelColor={item.enabled ? 'success' : undefined}
                statusLabelClassName="!relative !ml-[10px]"
              />
            ))}
          </Flex>
        </Flex>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default AutomationDrawer;
