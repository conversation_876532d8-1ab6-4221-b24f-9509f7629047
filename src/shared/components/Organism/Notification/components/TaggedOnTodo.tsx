import React from 'react';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { searchCandidateItem } from '@shared/utils/api/candidates';
import { routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import { ToggleNotificationList } from 'shared/utils/constants/NotificationVariants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import Layout, { IconSvg as Icon } from '../Notification.layout';
import { renderNotificationText } from '../utils/renderNotificationText';
import type { INotificationProps } from '../Notification.layout';

type OtherProps = {
  userId: string;
  userTitle: string;
};

const TaggedOnTodo: React.FC<INotificationProps<OtherProps>> = (props) => {
  const { data, onSeen, menuActions } = props;
  const { t } = useTranslation();
  const history = useHistory();

  const appDispatch = useGlobalDispatch();

  const handleView = () => {
    onSeen?.();
    if (data?.userId) history.push(routeNames.candidate.makeRoute(data.userId));

    setTimeout(() => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          entityId: data.userId,
          tab: 'todos',
          enablePagination: false,
          apiFunc: searchCandidateItem,
        },
      });
    }, 500);
  };
  const hasToggleNotification = ToggleNotificationList.includes(data.type);
  const description = renderNotificationText(
    translateReplacer(t('mentioned_y_i_a_t_o_a_candidate_profile'), [
      data?.userTitle,
    ]),
    [data?.userTitle]
  );

  return (
    <Layout
      hasToggleNotification={hasToggleNotification}
      onClick={handleView}
      menuActions={menuActions}
      icon={<Icon iconName="bell-on-s" type="far" color="brand" />}
      description={description}
      primaryAction={{
        closeModal: true,
        label: t('view_todo'),
        onClick: handleView,
      }}
      date={data?.createdDate}
      seen={data?.seen}
    />
  );
};

export default TaggedOnTodo;
