import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import Button from '@shared/uikit/Button';
import IconButton from '@shared/uikit/Button/IconButton';
import PopperPicker from '@shared/uikit/PopperPicker';
import storageApis from '@shared/utils/api/storage';
import { noteVisibilityOptions } from '@shared/utils/constants/enums/candidateDb';
import { storageEndPoints } from '@shared/utils/constants/servicesEndpoints';
import useTranslation from '@shared/utils/hooks/useTranslation';
import fileApi from 'shared/utils/api/file';
import classes from './NoteFields.module.scss';
import type { UpdateNoteFormFields } from '@shared/types/note';
import type { AttachmentPickerProps } from '@shared/uikit/AttachmentPicker/AttachmentPicker.component';

export function useNoteFields() {
  const { t } = useTranslation();
  const { values, setFieldValue } = useFormikContext<UpdateNoteFormFields>();

  const BODY = useMemo(
    () => ({
      name: 'body',
      cp: 'richtext',
      showEmoji: false,
      label: t('description'),
      changeWithDebounce: false,
      fixMentionDropdown: true,
      isMember: true,
      isFocus: true,
      disableNewLineWithEnter: true,
      noLeft: true,
      labelProps: { color: 'colorIconForth2' },
      isSidebar: false,
      required: true,
    }),
    [t]
  );

  const VISIBILITY = useMemo(
    () => ({
      name: 'visibility',
      cp: () => (
        <PopperPicker
          placement="top"
          size="sm"
          colorSchema="transparent"
          options={noteVisibilityOptions}
          value={values.visibility}
          onChange={(v) => {
            setFieldValue('visibility', v);
          }}
        />
      ),
      position: 'control-bar',
    }),
    [t, values.visibility, setFieldValue]
  );

  const VISIBILITY_DROP = useMemo(
    () => ({
      name: 'visibility',
      cp: 'dropdownSelect',
      label: t('visibility'),
      disabled: true,
      options: noteVisibilityOptions,
    }),
    [t]
  );

  const ATTACHMENT = useMemo(
    () => ({
      name: 'uploads',
      cp: 'attachmentPicker',
      showUploadList: false,
      type: 'image',
      uploadUrl: storageEndPoints.uploadPrivateFile,
      getApi: storageApis.getFile,
      visibleOptionalLabel: false,
      forceVisibleError: true,
      uploadApi: fileApi.uploadFile,
      classNames: {
        dropzoneOvveride: '',
      },
      onChange: (newFiles: AttachmentPickerProps['value']) => {
        const { fileIds = [] } = values ?? {};
        const newFileIds = newFiles.map(({ id }) => id);
        setTimeout(() => {
          setFieldValue('fileIds', [...newFileIds, ...fileIds]);
        }, 1000);
      },
      buttonComponent: (
        <Button
          label={t('add_attachments')}
          schema="semi-transparent"
          leftIcon="plus"
          leftType="far"
        />
      ),
    }),
    [t]
  );

  const ATTACHMENT_LIST = useMemo(
    () => ({
      name: 'fileIds',
      cp: ({ value }) => <PrivateAttachmentsList ids={value} horizontal />,
    }),
    []
  );

  return useMemo(
    () => ({
      BODY,
      VISIBILITY,
      VISIBILITY_DROP,
      ATTACHMENT,
      BODY_COMPOSE: {
        ...BODY,
        variant: 'comment-input',
        label: t('write_note'),
        labelProps: {
          className: classes.labelstyle,
        },
        className: classes.qlEditor,
        divider: true,
      },
      ATTACHMENT_LIST,
      ATTACHMENT_ICON: {
        ...ATTACHMENT,
        position: 'control-bar',
        buttonComponent: (
          <IconButton colorSchema="transparent" size="sm" name="paperclip" />
        ),
      },
    }),
    [BODY, VISIBILITY, VISIBILITY_DROP, ATTACHMENT, t, ATTACHMENT_LIST]
  );
}
