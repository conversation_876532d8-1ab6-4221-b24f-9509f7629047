import type { BEReview } from '@shared/types/review';
import Menu from '@shared/components/molecules/Menu/Menu';
import ReviewItem from '@shared/components/molecules/ReviewItem';
import ReviewItemSkeleton from '@shared/components/molecules/ReviewItem/ReviewItemSkeleton';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import type { MenuItem } from '@shared/types/components/Menu.type';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import * as API from '@shared/utils/api/candidates/reviews';
import { REVIEW_VISIBILITY_VALUES } from '@shared/utils/constants/enums/candidateDb';
import formValidator from '@shared/utils/form/formValidator';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useQueryClient } from '@tanstack/react-query';
import { useFormikContext } from 'formik';
import type { PropsWithChildren } from 'react';
import { useCallback, useMemo, useState } from 'react';
import MultipleInput from '@shared/components/molecules/MultipleInput';
import Rating from '@shared/components/molecules/Rating';
import IconButton from '@shared/uikit/Button/IconButton';
import { useManagerContext } from '@shared/components/Organism/CandidateManager/CandidateManager.context';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import { RichTextView } from '@shared/uikit/RichText';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import { getCandidatesReviews } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import { FooterWrap } from '../../components/FooterWrap';
import classes from '../tab.module.scss';
import { useReviewFields } from './hooks/useReviewFields';
import type {
  CandidateFormData,
  CandidateReviewRequest,
  ICandidateReview,
} from '@shared/types/candidates';
import type { FormikProps } from 'formik';
import { ORIGINAL_CANDIDATE } from '@shared/components/Organism/CandidateManager/contants';

const newReview: Partial<ICandidateReview> = {
  text: '',
  score: 1,
  visibility: REVIEW_VISIBILITY_VALUES.TEAM,
};

interface CandidateReviewsManagerProps {
  candidate: CandidateFormData;
}

export function CandidateReviewsManager({
  candidate,
}: CandidateReviewsManagerProps) {
  const [edit, setEdit] = useState<ICandidateReview | undefined>();
  const [formKey, setFormKey] = useState(0);
  const { selectedSummary } = useManagerContext();
  const { t } = useTranslation();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
    styles: {
      wrapper: '!w-[520px]',
    },
  });
  const queryClient = useQueryClient();

  const onSuccessHandler = () => {
    queryClient.invalidateQueries({
      exact: false,
      queryKey: [QueryKeys.candidateReviewes, selectedSummary],
    });
  };

  const {
    isLoading,
    data: reviews,
    refetch,
  } = useReactInfiniteQuery<BEReview>(
    [QueryKeys.candidateReviewes, selectedSummary],
    {
      func: getCandidatesReviews,
      extraProps: {
        candidateId: selectedSummary?.candidateId,
        participationId: selectedSummary?.id,
        isCandidateMode: selectedSummary?.type === ORIGINAL_CANDIDATE,
      },
      size: 10,
      spreadParams: true,
    }
  );

  const validationSchema = useMemo(
    () =>
      formValidator.object().shape({
        text: formValidator.string().required(),
      }),
    []
  );

  const handleSuccess = useCallback(
    (apiResponse: any, values?: any, formikRef?: FormikProps<any>) => {
      refetch();
      resetForm(formikRef);
    },
    [refetch]
  );

  const addApiFunc = useCallback(
    (data: CandidateReviewRequest) =>
      API.addCandidateReview({
        candidateId: selectedSummary?.candidateId,
        participationId: selectedSummary?.id,
        isCandidateMode: selectedSummary?.type === ORIGINAL_CANDIDATE,
        body: data,
      }),
    [selectedSummary]
  );

  const editApiFunc = useCallback(
    (data: CandidateReviewRequest) =>
      API.editCandidateReview({
        reviewId: edit?.id!,
        isCandidateMode: selectedSummary?.type === ORIGINAL_CANDIDATE,
        body: data,
      }),
    [edit?.id]
  );

  const deleteWithConfirm = (review: ICandidateReview) => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: () =>
          API.removeCandidateReview({
            reviewId: review.id,
            isCandidateMode: selectedSummary?.type === ORIGINAL_CANDIDATE,
          }),
        onSuccess: onSuccessHandler,
      },
    });
  };

  const menuItems = (review: ICandidateReview): MenuItem[] =>
    [
      {
        iconName: 'edit',
        label: t('edit'),
        onClick: (e: any) => {
          setEdit(review);
          setFormKey((k) => k + 1);
        },
      },
      {
        iconName: 'trash',
        label: t('delete'),
        onClick: () => deleteWithConfirm(review),
      },
    ] as const;

  const resetForm = (formikRef?: FormikProps<any>) => {
    formikRef?.resetForm();
    setEdit(undefined);
    setFormKey((k) => k + 1);
  };

  return (
    <>
      <Flex className={cnj('flex-1 !gap-20', classes.scrollArea)}>
        {isLoading ? (
          <ReviewItemSkeleton
            classNames={{
              root: 'border border-solid !border-techGray_20 bg-background',
              container: '!p-12',
            }}
          />
        ) : reviews?.length ? (
          reviews?.map((review) => (
            <ReviewItem
              key={review.id}
              variant="candidate"
              item={review}
              action={
                <Menu
                  menuItems={menuItems(review)}
                  menuPlacement="bottom-end"
                  menuItemSize={20}
                  classNames={{ itemIconWrapper: '!m-0', menu: '!p-0' }}
                />
              }
              cardWrapperProps={{
                classNames: {
                  container: '!p-12',
                  root: '!bg-background !border !border-solid !border-techGray_20',
                },
              }}
            />
          ))
        ) : (
          <EmptySearchResult
            className="h-full w-full items-center"
            title={t('no_review_found')}
            sectionMessage={t('no_review_found_desc')}
          />
        )}
      </Flex>
      <Form
        key={formKey}
        initialValues={edit ?? newReview}
        validationSchema={validationSchema}
        transform={candidateReviewTransform}
        onSuccess={handleSuccess}
        apiFunc={edit ? editApiFunc : addApiFunc}
        enableReinitialize
      >
        {edit ? (
          <Flex
            flexDir="row"
            alignItems="center"
            className="!w-full gap-8 px-16 py-8 bg-darkSecondary_hover"
          >
            <Flex className="gap-4 flex-1">
              <Typography
                font="400"
                size={15}
                height={18}
                color="secondaryDisabledText"
              >
                {t('edit_review')}
              </Typography>
              <Rating
                value={edit.score}
                onChange={(e, val) => setEdit({ ...edit, score: val || 1 })}
                spacing={3}
                size={18}
              />
              <RichTextView
                html={edit.text}
                typographyProps={{
                  size: 15,
                  color: 'thirdText',
                  height: 21,
                }}
                showMore
              />
            </Flex>
            <IconButton
              name="times"
              size="sm11"
              colorSchema="transparent"
              onClick={() => resetForm()}
            />
          </Flex>
        ) : null}
        <FooterWrap className="gap-10">
          <ReviewInputBody key={formKey} />
        </FooterWrap>
      </Form>
    </>
  );
}

function ReviewInputBody({ children }: PropsWithChildren) {
  const { SCORE, TEXT_COMPOSE, VISIBILITY } = useReviewFields();
  const { values } = useFormikContext<CandidateReviewRequest>();

  return (
    <>
      {children}
      <DynamicFormBuilder groups={[SCORE]} />
      <MultipleInput
        showAuthorAvatar
        groups={[TEXT_COMPOSE, VISIBILITY]}
        expand={!!values.text}
      />
    </>
  );
}

function candidateReviewTransform(
  data: Partial<ICandidateReview>
): CandidateReviewRequest {
  return {
    text: data.text ?? '',
    score: data.score || 0,
    visibility: data.visibility?.value ?? 'TEAM',
  };
}
