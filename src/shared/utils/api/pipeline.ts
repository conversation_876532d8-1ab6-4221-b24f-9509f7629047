import request from '../toolkit/request';
import { jobsEndpoints } from '../constants/servicesEndpoints';

// Types for auto-note
export interface PipelineAutoNote {
  id: string;
  pipelineId: string;
  visibility: 'EVERYONE' | 'ONLY_ME';
  text: string;
  fileIds: string[];
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    role: string;
    avatarUrl?: string;
  };
}

export interface PipelineAutoNoteRequest {
  visibility: 'EVERYONE' | 'ONLY_ME';
  text: string;
  fileIds: string[];
}

// The new response type for getPipelineAutoNote
export interface PipelineAutoNoteResponse {
  pipeline: {
    id: string;
    title: string;
    type: string;
    stageType: string;
    applicantTrack: boolean;
    order: string;
    color: string;
    count: string;
    autoMovementEnabled: boolean;
    autoRejectionEnabled: boolean;
    autoNoteEnabled: boolean;
    autoTodoEnabled: boolean;
    autoReplyEnabled: boolean;
    autoInterviewEnabled: boolean;
    assessmentIds: string[];
  };
  user: {
    id: string;
    email: string;
    username: string;
    imageUrl: string;
    croppedImageUrl: string;
    croppedImageData: string;
    name: string;
    surname: string;
    location: any;
    occupationId: string;
    occupationName: string;
    birthDate: string;
    phone: string;
    gender: string;
    genderDescription: string | null;
    allowPageRoleAssign: boolean;
  };
  text: string;
  visibility: 'EVERYONE' | 'ONLY_ME';
  fileIds: string[] | null;
}

// GET auto-note for a pipeline
export const getPipelineAutoNote = async (
  pipelineId: number
): Promise<PipelineAutoNoteResponse | null> => {
  try {
    const { data } = await request.get(
      jobsEndpoints.getPipelineAutoNote(pipelineId.toString())
    );
    return data;
  } catch (e) {
    throw e;
  }
};

export const putPipelineAutoNote = async (
  pipelineId: number,
  body: PipelineAutoNoteRequest
): Promise<PipelineAutoNoteResponse> => {
  try {
    const { data } = await request.put(
      jobsEndpoints.getPipelineAutoNote(pipelineId.toString()),
      body
    );
    return data;
  } catch (e) {
    throw e;
  }
};

export const deletePipelineAutoNote = async (
  pipelineId: number
): Promise<void> => {
  try {
    await request.delete(
      jobsEndpoints.getPipelineAutoNote(pipelineId.toString())
    );
  } catch (e) {
    throw e;
  }
};

// Types for auto-todo
export interface PipelineAutoTodoRequest {
  title: string;
  description: string;
  assigneeUserId: number;
  allTeamMembersTagged: boolean;
  taggedUserIds: number[];
  fileIds: number[];
}

export interface PipelineAutoTodoResponse {
  pipeline: {
    id: number;
    title: string;
    type: string;
    stageType: string;
    applicantTrack: boolean;
    order: number;
    color: string;
    count: number;
    autoMovementEnabled: boolean;
    autoRejectionEnabled: boolean;
    autoNoteEnabled: boolean;
    autoTodoEnabled: boolean;
    autoReplyEnabled: boolean;
    autoInterviewEnabled: boolean;
    assessmentIds: number[];
  };
  user: {
    id: number;
    email: string;
    username: string;
    imageUrl: string;
    croppedImageUrl: string;
    croppedImageData: string;
    name: string;
    surname: string;
    location: any;
    occupationId: number;
    occupationName: string;
    birthDate: string;
    phone: string;
    gender: string;
    genderDescription: string;
    allowPageRoleAssign: boolean;
  };
  assigneeUser: {
    id: number;
    email: string;
    username: string;
    imageUrl: string;
    croppedImageUrl: string;
    croppedImageData: string;
    name: string;
    surname: string;
    location: any;
    occupationId: number;
    occupationName: string;
    birthDate: string;
    phone: string;
    gender: string;
    genderDescription: string;
    allowPageRoleAssign: boolean;
  };
  title: string;
  text: string;
  fileIds: number[];
}

export type PipelineAutoReplyResponse = {
  pipeline: {
    id: number;
    title: string;
    type: 'REVIEW' | string;
    stageType: 'HIRING' | string;
    applicantTrack: boolean;
    order: number;
    color: string;
    count: number;
    autoMovementEnabled: boolean;
    autoRejectionEnabled: boolean;
    autoNoteEnabled: boolean;
    autoTodoEnabled: boolean;
    autoReplyEnabled: boolean;
    autoInterviewEnabled: boolean;
    autoAssessmentEnabled: boolean;
    assessmentId: number;
  };
  user: {
    id: number;
    email: string;
    username: string;
    imageUrl: string;
    croppedImageUrl: string;
    croppedImageData: string;
    name: string;
    surname: string;
    location: {
      id: number;
      externalId: string;
      title: string;
      titleAscii: string;
      lat: string;
      lon: string;
      cityName: string;
      cityCode: string;
      stateName: string;
      stateCode: string;
      stateType: string;
      countryName: string;
      countryCode: string;
      legacyCodes: string[];
      access: 'ONLY_ME' | string;
      category: string;
      detail: string;
      preview: boolean;
    };
    occupationId: number;
    occupationName: string;
    birthDate: string; // ISO date string
    phone: string;
    gender: 'MALE' | 'FEMALE' | string;
    genderDescription: string;
    allowPageRoleAssign: boolean;
    fullName: string;
  };
  timeDelay: 'IMMEDIATELY' | string;
  templateId: string;
  templateSubject: string;
  templateBody: string;
  templateFileIds: number[];
  hasFollowUpMessage: boolean;
  followupMessagePeriod: '_3_DAYS' | string;
  followupMessageTimeDelay: 'IMMEDIATELY' | string;
  followupMessageTemplateId: number;
  followupMessageTemplateSubject: string;
  followupMessageTemplateBody: string;
  followupMessageTemplateFileIds: number[];
};

export type PipelineAutoReplyRequest = {
  templateId: number;
};

// GET auto-todo for a pipeline
export const getPipelineAutoTodo = async (
  pipelineId: number
): Promise<PipelineAutoTodoResponse | null> => {
  try {
    const { data } = await request.get(
      jobsEndpoints.getPipelineAutoTodo(pipelineId.toString())
    );
    return data;
  } catch (e) {
    throw e;
  }
};

export const putPipelineAutoTodo = async (
  pipelineId: number,
  body: PipelineAutoTodoRequest
): Promise<PipelineAutoTodoResponse> => {
  try {
    const { data } = await request.put(
      jobsEndpoints.getPipelineAutoTodo(pipelineId.toString()),
      body
    );
    return data;
  } catch (e) {
    throw e;
  }
};

export const deletePipelineAutoTodo = async (
  pipelineId: number
): Promise<void> => {
  try {
    await request.delete(
      jobsEndpoints.getPipelineAutoTodo(pipelineId.toString())
    );
  } catch (e) {
    throw e;
  }
};

export const getPipelineAutoReply = async (
  pipelineId: number
): Promise<PipelineAutoReplyResponse | null> => {
  const { data } = await request.get(
    jobsEndpoints.getPipelineAutoReply(pipelineId.toString())
  );

  return data;
};

export const putPipelineAutoReply = async ({
  body,
  pipelineId,
}: {
  pipelineId: number;
  body: PipelineAutoReplyRequest;
}): Promise<PipelineAutoReplyResponse> => {
  const { data } = await request.put(
    jobsEndpoints.getPipelineAutoReply(pipelineId.toString()),
    body
  );

  return data;
};

export const deletePipelineAutoReply = async (
  pipelineId: number
): Promise<void> => {
  await request.delete(
    jobsEndpoints.getPipelineAutoReply(pipelineId.toString())
  );
};

export const getPipelineAutoMessage = async (
  pipelineId: number
): Promise<PipelineAutoReplyResponse | null> => {
  const { data } = await request.get(
    jobsEndpoints.getPipelineAutoMessage(pipelineId.toString())
  );

  return data;
};

export const putPipelineAutoMessage = async ({
  body,
  pipelineId,
}: {
  pipelineId: number;
  body: PipelineAutoReplyRequest;
}): Promise<PipelineAutoReplyResponse> => {
  const { data } = await request.put(
    jobsEndpoints.getPipelineAutoMessage(pipelineId.toString()),
    body
  );

  return data;
};

export const deletePipelineAutoMessage = async (
  pipelineId: number
): Promise<void> => {
  await request.delete(
    jobsEndpoints.getPipelineAutoMessage(pipelineId.toString())
  );
};

export const putPipelineAutoInterview = async ({
  body,
  pipelineId,
}: {
  pipelineId: number;
  body: {
    templateId: number;
    timesheetId: number;
    attendeeIds: number[];
  };
}): Promise<PipelineAutoReplyResponse> => {
  const { data } = await request.put(
    jobsEndpoints.getPipelineAutoInterview(pipelineId.toString()),
    body
  );

  return data;
};

export const deletePipelineAutoInterview = async (
  pipelineId: number
): Promise<void> => {
  await request.delete(
    jobsEndpoints.getPipelineAutoInterview(pipelineId.toString())
  );
};

export const getPipelineAutoInterview = async (
  pipelineId: number
): Promise<PipelineAutoReplyResponse | null> => {
  const { data } = await request.get(
    jobsEndpoints.getPipelineAutoInterview(pipelineId.toString())
  );

  return data;
};

export default {
  getPipelineAutoNote,
  putPipelineAutoNote,
  deletePipelineAutoNote,
  getPipelineAutoTodo,
  putPipelineAutoTodo,
  deletePipelineAutoTodo,
};
