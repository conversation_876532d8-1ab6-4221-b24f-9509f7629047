import { normalizeCandidateReview } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import request from '../../toolkit/request';
import type {
  BECandidateReview,
  CandidateReviewRequest,
  ICandidateReview,
} from '@shared/types/candidates';
import type { PaginateResponse } from '@shared/types/response';
import {
  candidateEndpoints,
  jobsEndpoints,
} from '@shared/utils/constants/servicesEndpoints';

export const getCandidatesReviews = async (params: {
  candidateId?: string | number;
  participationId?: string;
  isCandidateMode: boolean;
  page?: number;
  size?: number;
  onlyDone?: boolean;
}): Promise<PaginateResponse<ICandidateReview>> => {
  const { candidateId, isCandidateMode, participationId, ...otherParams } =
    params;

  const { data } = await request.get<PaginateResponse<BECandidateReview>>(
    isCandidateMode
      ? candidateEndpoints.getCandidateReviews(candidateId)
      : jobsEndpoints.getParticipationReviews(participationId),
    { params: otherParams }
  );
  const { content, ...rest } = data;

  return {
    ...rest,
    content: content.map(normalizeCandidateReview),
  };
};

export const addCandidateReview = async (params: {
  candidateId: string;
  isCandidateMode: boolean;
  participationId?: string;
  body: CandidateReviewRequest;
}) => {
  const { candidateId, isCandidateMode, participationId, body } = params;
  const url = isCandidateMode
    ? candidateEndpoints.addCandidateReview(candidateId)
    : jobsEndpoints.addParticipationReview(participationId);
  const { data } = await request.post<BECandidateReview>(url, body);

  return data ?? {};
};

export const editCandidateReview = async ({
  reviewId,
  isCandidateMode,
  body,
}: {
  reviewId: string;
  isCandidateMode: boolean;
  body: CandidateReviewRequest;
}) => {
  const url = isCandidateMode
    ? candidateEndpoints.editCandidateReview(reviewId)
    : jobsEndpoints.editParticipationReview(reviewId);
  return request.put<BECandidateReview>(url, body);
};

export const removeCandidateReview = async ({
  reviewId,
  isCandidateMode,
}: {
  reviewId: string;
  isCandidateMode: boolean;
}) => {
  const url = isCandidateMode
    ? candidateEndpoints.removeCandidateReview(reviewId)
    : jobsEndpoints.removeParticipationReview(reviewId);
  const { data } = await request.delete<BECandidateReview>(url);

  return data;
};
