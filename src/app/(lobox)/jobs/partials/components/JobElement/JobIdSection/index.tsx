import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import CopyEntityId from 'shared/components/molecules/CopyEntityId';
import useJobElement from '../../../hooks/useJobElement';

const JobIdSection = () => {
  const { t } = useTranslation();
  const { getJobPropValue } = useJobElement();
  const jobId = getJobPropValue('id');

  return (
    <CopyEntityId
      title={t('job_id')}
      entityId={jobId}
      tooltip={t('y_c_search_th_j_id')}
    />
  );
};

export default JobIdSection;
